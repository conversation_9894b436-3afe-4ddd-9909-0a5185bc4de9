import json
import os
from collections import OrderedDict
from datetime import datetime
from typing import Dict, List, Optional, Any

class ConfigManager:
    def __init__(self, config_file="configs/app/config.json", max_groups=100):
        self.config_file = config_file
        self.max_groups = max_groups
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件，如果不存在则创建默认配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f, object_pairs_hook=OrderedDict)
                    print(f"✅ 成功加载配置文件: {self.config_file}")

                    # 检查并迁移旧格式配置
                    if self._needs_migration(config):
                        print("🔄 检测到旧格式配置，正在迁移...")
                        config = self._migrate_config(config)
                        print("✅ 配置迁移完成")

                    return config
            except json.JSONDecodeError as e:
                print(f"❌ JSON格式错误 {self.config_file}: {e}")
                print("🔧 使用默认配置")
                return self.create_default_config()
            except Exception as e:
                print(f"❌ 加载配置文件失败 {self.config_file}: {e}")
                print("🔧 使用默认配置")
                return self.create_default_config()
        else:
            print(f"⚠️ 配置文件不存在: {self.config_file}")
            print("🔧 创建默认配置")
            return self.create_default_config()

    def _needs_migration(self, config: Dict[str, Any]) -> bool:
        """检查是否需要迁移配置"""
        # 如果没有categories字段，说明是旧格式
        return "categories" not in config

    def _migrate_config(self, old_config: Dict[str, Any]) -> Dict[str, Any]:
        """迁移旧格式配置到新格式"""
        try:
            # 创建新的配置结构
            new_config = OrderedDict()
            new_config["last_used"] = old_config.get("last_used", "default")

            # 创建默认三级分类结构
            new_config["categories"] = OrderedDict({
                "政府机构": {
                    "description": "从旧配置迁移的政府机构分类",
                    "subcategories": {
                        "人大系统": {
                            "description": "人大系统配置",
                            "subcategories": {
                                "地方人大": {
                                    "configs": [],
                                    "description": "地方人大配置"
                                }
                            }
                        }
                    },
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            })

            # 迁移配置组
            new_config["groups"] = OrderedDict()
            old_groups = old_config.get("groups", {})

            for group_name, group_config in old_groups.items():
                # 为每个配置组添加三级分类路径和缓存信息
                new_group_config = group_config.copy()
                new_group_config["category_path"] = "政府机构/人大系统/地方人大"

                # 添加缓存结构
                if "cache" not in new_group_config:
                    new_group_config["cache"] = {
                        "last_update": None,
                        "last_urls": [],
                        "total_articles": 0,
                        "success_rate": 0.0,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }

                new_config["groups"][group_name] = new_group_config

                # 添加到默认三级分类
                new_config["categories"]["政府机构"]["subcategories"]["人大系统"]["subcategories"]["地方人大"]["configs"].append(group_name)

            # 保存迁移后的配置
            self.config = new_config
            self.save_config()

            return new_config

        except Exception as e:
            print(f"❌ 配置迁移失败: {e}")
            return old_config
    
    def create_default_config(self):
        """创建默认配置，包含所有参数字段和新的三级分类、缓存结构"""
        return OrderedDict({
            "last_used": "default",
            "categories": OrderedDict({
                "政府机构": {
                    "description": "政府机构相关配置",
                    "subcategories": {
                        "人大系统": {
                            "description": "人大系统配置",
                            "subcategories": {
                                "全国人大": {
                                    "configs": [],
                                    "description": "全国人大配置"
                                },
                                "地方人大": {
                                    "configs": ["default"],
                                    "description": "地方人大配置"
                                }
                            }
                        },
                        "政协系统": {
                            "description": "政协系统配置",
                            "subcategories": {
                                "全国政协": {
                                    "configs": [],
                                    "description": "全国政协配置"
                                },
                                "地方政协": {
                                    "configs": [],
                                    "description": "地方政协配置"
                                }
                            }
                        }
                    },
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            }),
            "groups": OrderedDict({
                "default": {
                    "category_path": "政府机构/人大系统/地方人大",
                    "input_url": "",
                    "base_url": "",
                    "max_pages": "0",
                    "list_container_selector": "",
                    "article_item_selector": "",
                    "title_selectors": [],  # 使用复数形式，支持多选择器
                    "content_selectors": [],
                    "content_type": "CSS",
                    "date_selectors": [],  # 使用复数形式，支持多选择器
                    "source_selectors": [],  # 使用复数形式，支持多选择器
                    "page_suffix": "index_{n}.html",
                    "page_suffix_start": 1,
                    "url_mode": "absolute",
                    "browser": "Firefox",
                    "headless": True,
                    "window_size": "",
                    "page_load_strategy": "normal",
                    "collect_links": True,
                    "mode": "balance",
                    "filters": [],
                    "export_filename": "",
                    "classid": "",
                    "file_format": "CSV",
                    "cache": {
                        "last_update": None,
                        "last_urls": [],
                        "total_articles": 0,
                        "success_rate": 0.0,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }
                }
            })
        })
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_groups(self):
        """获取所有配置组名称"""
        return list(self.config["groups"].keys())
    
    def get_group(self, group_name):
        """获取指定配置组"""
        return self.config["groups"].get(group_name)
    
    def get_current_group(self):
        """获取当前使用的配置组"""
        return self.config["last_used"]
    
    def add_group(self, group_name, config_data, category_path="政府机构/人大系统/地方人大"):
        """添加新的配置组（合并旧配置，防止字段丢失）"""
        try:
            groups = self.config["groups"]

            # 确保三级分类存在
            parts = category_path.split("/")
            if len(parts) == 3:
                parent, sub, child = parts
                self.add_three_level_category(parent, sub, child)

            # 合并旧配置，防止字段丢失
            if group_name in groups:
                old_config = groups[group_name]
                merged_config = old_config.copy()
                # 嵌套字典（如module_config、pagination_config）也要合并
                for k, v in config_data.items():
                    if isinstance(v, dict) and k in merged_config and isinstance(merged_config[k], dict):
                        merged = merged_config[k].copy()
                        merged.update(v)
                        merged_config[k] = merged
                    else:
                        merged_config[k] = v
                groups[group_name] = merged_config

                # 更新分类路径信息
                old_category_path = merged_config.get("category_path")
                if old_category_path != category_path:
                    # 从旧分类中移除
                    if old_category_path:
                        old_configs = self.get_configs_by_category_path(old_category_path)
                        if old_configs and group_name in old_configs:
                            # 实际移除逻辑在move_config_to_category_path中处理
                            pass

                    # 更新分类路径
                    merged_config["category_path"] = category_path
            else:
                # 如果达到最大组数限制，删除最旧的一个
                if len(groups) >= self.max_groups:
                    oldest = next(iter(groups))
                    # 从分类中也移除
                    oldest_config = groups[oldest]
                    oldest_category = oldest_config.get("category")
                    if oldest_category and oldest_category in self.config["categories"]:
                        if oldest in self.config["categories"][oldest_category]["configs"]:
                            self.config["categories"][oldest_category]["configs"].remove(oldest)
                    del groups[oldest]

                # 添加分类路径和缓存结构
                config_data["category_path"] = category_path
                if "cache" not in config_data:
                    config_data["cache"] = {
                        "last_update": None,
                        "last_urls": [],
                        "total_articles": 0,
                        "success_rate": 0.0,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }

                groups[group_name] = config_data

                # 添加到三级分类中
                if len(parts) == 3:
                    parent, sub, child = parts
                    child_cat = (self.config.get("categories", {})
                               .get(parent, {})
                               .get("subcategories", {})
                               .get(sub, {})
                               .get("subcategories", {})
                               .get(child, {}))
                    if "configs" in child_cat and group_name not in child_cat["configs"]:
                        child_cat["configs"].append(group_name)

            self.config["last_used"] = group_name
            self.save_config()
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def delete_group(self, group_name):
        """删除配置组"""
        if group_name in self.config["groups"]:
            # 从三级分类中移除
            config = self.config["groups"][group_name]
            category_path = config.get("category_path")
            if category_path:
                parts = category_path.split("/")
                if len(parts) == 3:
                    parent, sub, child = parts
                    child_cat = (self.config.get("categories", {})
                               .get(parent, {})
                               .get("subcategories", {})
                               .get(sub, {})
                               .get("subcategories", {})
                               .get(child, {}))
                    if "configs" in child_cat and group_name in child_cat["configs"]:
                        child_cat["configs"].remove(group_name)

            del self.config["groups"][group_name]

            # 如果删除的是当前使用的配置组，重置为默认
            if self.config["last_used"] == group_name:
                self.config["last_used"] = "default" if "default" in self.config["groups"] else next(iter(self.config["groups"].keys()), None)

            self.save_config()
            return True
        return False
    
    def set_current_group(self, group_name):
        """设置当前使用的配置组"""
        if group_name in self.config["groups"]:
            self.config["last_used"] = group_name
            self.save_config()
            return True
        return False

    # ==================== 新增：分类管理功能 ====================

    def get_categories(self) -> Dict[str, Any]:
        """获取所有分类"""
        return self.config.get("categories", {})

    def add_category(self, category_name: str, description: str = "") -> bool:
        """添加新分类"""
        try:
            if "categories" not in self.config:
                self.config["categories"] = OrderedDict()

            if category_name not in self.config["categories"]:
                self.config["categories"][category_name] = {
                    "configs": [],
                    "description": description,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
                self.save_config()
                return True
            return False
        except Exception as e:
            print(f"添加分类失败: {e}")
            return False

    def delete_category(self, category_name: str) -> bool:
        """删除分类（将其下的配置组移动到默认分类）"""
        try:
            if category_name not in self.config.get("categories", {}):
                return False

            # 确保默认分类存在
            if "默认分类" not in self.config["categories"]:
                self.add_category("默认分类", "默认配置分类")

            # 移动配置组到默认分类
            configs_to_move = self.config["categories"][category_name].get("configs", [])
            for config_name in configs_to_move:
                if config_name in self.config["groups"]:
                    self.config["groups"][config_name]["category"] = "默认分类"
                    if config_name not in self.config["categories"]["默认分类"]["configs"]:
                        self.config["categories"]["默认分类"]["configs"].append(config_name)

            # 删除分类
            del self.config["categories"][category_name]
            self.save_config()
            return True
        except Exception as e:
            print(f"删除分类失败: {e}")
            return False

    def move_config_to_category(self, config_name: str, target_category: str) -> bool:
        """将配置组移动到指定分类"""
        try:
            if config_name not in self.config["groups"]:
                return False

            if target_category not in self.config["categories"]:
                return False

            # 从原分类中移除
            old_category = self.config["groups"][config_name].get("category")
            if old_category and old_category in self.config["categories"]:
                if config_name in self.config["categories"][old_category]["configs"]:
                    self.config["categories"][old_category]["configs"].remove(config_name)

            # 添加到新分类
            self.config["groups"][config_name]["category"] = target_category
            if config_name not in self.config["categories"][target_category]["configs"]:
                self.config["categories"][target_category]["configs"].append(config_name)

            self.save_config()
            return True
        except Exception as e:
            print(f"移动配置组失败: {e}")
            return False

    def get_configs_by_category(self, category_name: str) -> List[str]:
        """获取指定分类下的所有配置组（兼容旧版本）"""
        category = self.config.get("categories", {}).get(category_name, {})
        return category.get("configs", [])

    # ==================== 新增：三级分类管理功能 ====================

    def get_parent_categories(self) -> List[str]:
        """获取所有父级分类"""
        return list(self.config.get("categories", {}).keys())

    def get_sub_categories(self, parent_category: str) -> List[str]:
        """获取指定父级分类下的次级分类"""
        parent = self.config.get("categories", {}).get(parent_category, {})
        return list(parent.get("subcategories", {}).keys())

    def get_child_categories(self, parent_category: str, sub_category: str) -> List[str]:
        """获取指定次级分类下的子级分类"""
        parent = self.config.get("categories", {}).get(parent_category, {})
        sub = parent.get("subcategories", {}).get(sub_category, {})
        return list(sub.get("subcategories", {}).keys())

    def get_configs_by_category_path(self, category_path: str) -> List[str]:
        """根据分类路径获取配置组列表"""
        try:
            parts = category_path.split("/")
            if len(parts) != 3:
                return []

            parent, sub, child = parts
            parent_cat = self.config.get("categories", {}).get(parent, {})
            sub_cat = parent_cat.get("subcategories", {}).get(sub, {})
            child_cat = sub_cat.get("subcategories", {}).get(child, {})

            return child_cat.get("configs", [])
        except:
            return []

    def add_three_level_category(self, parent: str, sub: str, child: str, description: str = "") -> bool:
        """添加三级分类"""
        try:
            categories = self.config.setdefault("categories", OrderedDict())

            # 确保父级分类存在
            if parent not in categories:
                categories[parent] = {
                    "description": f"{parent}分类",
                    "subcategories": OrderedDict(),
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

            # 确保次级分类存在
            parent_cat = categories[parent]
            subcategories = parent_cat.setdefault("subcategories", OrderedDict())
            if sub not in subcategories:
                subcategories[sub] = {
                    "description": f"{sub}分类",
                    "subcategories": OrderedDict()
                }

            # 添加子级分类
            sub_cat = subcategories[sub]
            child_subcategories = sub_cat.setdefault("subcategories", OrderedDict())
            if child not in child_subcategories:
                child_subcategories[child] = {
                    "configs": [],
                    "description": description or f"{child}配置"
                }

                # 更新时间戳
                parent_cat["updated_at"] = datetime.now().isoformat()
                self.save_config()
                return True

            return False
        except Exception as e:
            print(f"添加三级分类失败: {e}")
            return False

    def move_config_to_category_path(self, config_name: str, category_path: str) -> bool:
        """将配置组移动到指定的三级分类路径"""
        try:
            if config_name not in self.config["groups"]:
                return False

            parts = category_path.split("/")
            if len(parts) != 3:
                return False

            parent, sub, child = parts

            # 验证分类路径是否存在
            configs = self.get_configs_by_category_path(category_path)
            if configs is None:
                return False

            # 从旧分类中移除
            old_category_path = self.config["groups"][config_name].get("category_path")
            if old_category_path:
                old_configs = self.get_configs_by_category_path(old_category_path)
                if old_configs and config_name in old_configs:
                    # 从旧分类中移除
                    old_parts = old_category_path.split("/")
                    if len(old_parts) == 3:
                        old_parent, old_sub, old_child = old_parts
                        old_child_cat = (self.config.get("categories", {})
                                       .get(old_parent, {})
                                       .get("subcategories", {})
                                       .get(old_sub, {})
                                       .get("subcategories", {})
                                       .get(old_child, {}))
                        if "configs" in old_child_cat and config_name in old_child_cat["configs"]:
                            old_child_cat["configs"].remove(config_name)

            # 添加到新分类
            new_child_cat = (self.config.get("categories", {})
                           .get(parent, {})
                           .get("subcategories", {})
                           .get(sub, {})
                           .get("subcategories", {})
                           .get(child, {}))

            if "configs" in new_child_cat:
                if config_name not in new_child_cat["configs"]:
                    new_child_cat["configs"].append(config_name)

                # 更新配置组的分类路径
                self.config["groups"][config_name]["category_path"] = category_path

                self.save_config()
                return True

            return False
        except Exception as e:
            print(f"移动配置组失败: {e}")
            return False

    # ==================== 新增：缓存管理功能 ====================

    def get_config_cache(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取配置组的缓存信息"""
        config = self.config["groups"].get(config_name)
        if config:
            return config.get("cache", {})
        return None

    def update_config_cache(self, config_name: str, cache_data: Dict[str, Any]) -> bool:
        """更新配置组的缓存信息"""
        try:
            if config_name not in self.config["groups"]:
                return False

            if "cache" not in self.config["groups"][config_name]:
                self.config["groups"][config_name]["cache"] = {}

            # 更新缓存数据
            self.config["groups"][config_name]["cache"].update(cache_data)
            self.config["groups"][config_name]["cache"]["updated_at"] = datetime.now().isoformat()

            self.save_config()
            return True
        except Exception as e:
            print(f"更新缓存失败: {e}")
            return False

    def add_url_to_cache(self, config_name: str, url: str, title: str, timestamp: str = None) -> bool:
        """添加URL到缓存记录（保持最新3条）"""
        try:
            if config_name not in self.config["groups"]:
                return False

            if "cache" not in self.config["groups"][config_name]:
                self.config["groups"][config_name]["cache"] = {
                    "last_update": None,
                    "last_urls": [],
                    "total_articles": 0,
                    "success_rate": 0.0,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

            cache = self.config["groups"][config_name]["cache"]

            # 创建新的URL记录
            url_record = {
                "url": url,
                "title": title,
                "timestamp": timestamp or datetime.now().isoformat()
            }

            # 检查是否已存在相同URL，如果存在则更新
            existing_index = -1
            for i, existing_url in enumerate(cache.get("last_urls", [])):
                if existing_url["url"] == url:
                    existing_index = i
                    break

            if existing_index >= 0:
                # 更新现有记录
                cache["last_urls"][existing_index] = url_record
            else:
                # 添加新记录
                cache.setdefault("last_urls", []).insert(0, url_record)

            # 保持最新3条记录
            cache["last_urls"] = cache["last_urls"][:3]

            # 更新最后更新时间
            cache["last_update"] = datetime.now().isoformat()
            cache["updated_at"] = datetime.now().isoformat()

            self.save_config()
            return True
        except Exception as e:
            print(f"添加URL到缓存失败: {e}")
            return False

    def get_last_urls(self, config_name: str) -> List[Dict[str, str]]:
        """获取配置组的最后3条URL记录"""
        cache = self.get_config_cache(config_name)
        if cache:
            return cache.get("last_urls", [])
        return []

    def clear_config_cache(self, config_name: str) -> bool:
        """清空配置组的缓存"""
        try:
            if config_name not in self.config["groups"]:
                return False

            self.config["groups"][config_name]["cache"] = {
                "last_update": None,
                "last_urls": [],
                "total_articles": 0,
                "success_rate": 0.0,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            self.save_config()
            return True
        except Exception as e:
            print(f"清空缓存失败: {e}")
            return False